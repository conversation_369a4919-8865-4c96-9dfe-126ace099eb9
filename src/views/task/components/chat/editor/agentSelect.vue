<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 12:01:21
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/editor/agentSelect.vue
 * @Description: 选择器组件
-->
<template>
  <div class="agentSelect" :data-key="slotData.id" contenteditable="false" @click.stop="openSelectDropdown = true">
    <!-- <img class="widgetBuffer" /> -->
    <a-select
      style="visibility: hidden; width: 0"
      ref="selectRef"
      :getPopupContainer="getPopupContainer"
      :bordered="false"
      :dropdownMatchSelectWidth="false"
      :showArrow="false"
      :open="openSelectDropdown"
      @change="handleChange"
    >
      <a-select-option v-for="item in selectOptions" :key="item" :value="item">{{ item }}</a-select-option>
    </a-select>
    <!-- 真正要是显示出来的内容 -->
    <span
      :class="['select-content', isEmpty(props.value) ? 'show-placeholder' : '']"
      :data-placeholder="placeholder"
      ref="inputRef"
      contenteditable="false"
      >{{ props.value }}</span
    >
    <!-- <img class="widgetBuffer" /> -->
  </div>
</template>

<script setup lang="ts">
import { computed, ref, useTemplateRef } from 'vue'
import type { PromptItemType } from './index.vue'
import { onClickOutside, useVModel } from '@vueuse/core'
import { isEmpty } from 'lodash-es'
import { selectProps } from 'ant-design-vue/es/select'

// 组件接收的属性定义
interface Props {
  value: string | undefined // 输入框的值
  placeholder?: string // 占位符文本
  slotData: PromptItemType // 插槽数据
}

const props = defineProps<Props>()

// 定义组件触发的事件
const emit = defineEmits<{
  'update:value': [value: string] // v-model双向绑定事件
  change: [value: string] // change事件
  navigate: [direction: 'left' | 'right', slotData: PromptItemType] // 导航事件
}>()

const getPopupContainer = () => document.body

const openSelectDropdown = ref(false)
// 输入框DOM引用
const selectRef = useTemplateRef<HTMLElement>('selectRef')
// 点击外部关闭下拉框
onClickOutside(selectRef, event => {
  openSelectDropdown.value = false
})

// 处理options
const selectOptions = computed(() => {
  return props.slotData.props?.options?.split(',') || []
})

function handleChange(val: string) {
  emit('update:value', val)
  emit('change', val)
  openSelectDropdown.value = false
}

// 暴露给父组件的方法和属性
defineExpose({
  selectRef
})

// 处理键盘事件
function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const isAtStart = range.startOffset === 0
      const isAtEnd = range.startContainer.textContent
        ? range.startOffset === range.startContainer.textContent.length
        : false

      // 在空内容时，或在内容的最左/最右边时触发导航
      if (
        (props.value && !props.value.trim()) ||
        (e.key === 'ArrowLeft' && isAtStart) ||
        (e.key === 'ArrowRight' && isAtEnd)
      ) {
        // e.preventDefault()
        emit('navigate', e.key === 'ArrowLeft' ? 'left' : 'right', props.slotData)
      }
    }
  }
}
</script>

<style scoped lang="less">
// 定义插槽样式变量
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

// 输入框容器样式
.agentSelect {
  display: inline-flex;
  align-items: center;
  // vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
  cursor: text;
  outline: none;

  // 输入内容区域样式
  .select-content {
    outline: none;
    min-width: 1px;
    color: #6553ee;

    // 只在空内容且有 show-placeholder 类时显示 placeholder
    &.show-placeholder:empty::before {
      content: attr(data-placeholder);
      color: #f4f0ff;
      pointer-events: none;
    }
  }
}

// 辅助元素样式
.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}
</style>
