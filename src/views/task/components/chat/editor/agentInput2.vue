<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-18 09:41:38
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 10:56:27
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/editor/agentInput2.vue
 * @Description: 
-->
<template>
  <span
    ref="inputRef"
    contenteditable="true"
    @keydown="handleKeydown"
    @keyup="handleKeyup"
    @input="handleInput"
    @blur="handleBlur"
    @copy="handleCopy"
    @paste="handlePaste"
    @cut="handleCut"
    @keydown.left="handleLeftKey"
    @keydown.right="handleRightKey"
    @click.stop="handleClick"
  >
    <img class="widgetBuffer" aria-hidden="true" />
    <span class="slot-side-left" contenteditable="false" />
    <img class="widgetBuffer" aria-hidden="true" />
    <span class="slot-content"></span>
    <img class="widgetBuffer" aria-hidden="true" />
    <span contenteditable="false" class="placeholder">placeholder</span>
    <span class="slot-side-right" contenteditable="false" />
    <img class="widgetBuffer" aria-hidden="true" />
  </span>
</template>

<script setup lang="ts">
import { getCurrentInstance, useTemplateRef } from 'vue'

const inputRef = useTemplateRef('inputRef')
const vm = getCurrentInstance()

function handleClick() {
  console.log(1212121, vm)
  const contentDom = inputRef.value?.querySelector('.slot-content')
  if (contentDom) {
    const range = document.createRange()
    const selection = window.getSelection()
    range.setStart(contentDom, 0)
    // range.selectNodeContents(contentDom)
    selection?.removeAllRanges()
    selection?.addRange(range)
  }
}
function handleKeydown(e) {
  e.stopPropagation()
  console.log('handleKeydown(e)', e)
}
function handleKeyup(e) {
  e.stopPropagation()
  console.log('handleKeyup(e)', e)
}
function handleInput(e) {
  e.stopPropagation()
  console.log('handleInput(e)', e)
}
function handleBlur(e) {
  e.stopPropagation()
  console.log('handleBlur(e)', e)
}
function handleCopy(e) {
  e.stopPropagation()
  console.log('handleCopy(e)', e)
}
function handlePaste(e) {
  e.stopPropagation()
  console.log('handlePaste(e)', e)
}
function handleCut(e) {
  e.stopPropagation()
  console.log('handleCut(e)', e)
}
function handleLeftKey(e) {
  e.stopPropagation()
  console.log('handleLeftKey(e)', e)
}
function handleRightKey(e) {
  e.stopPropagation()
  console.log('handleRightKey(e)', e)
}
</script>

<style scoped lang="less">
@slotHeight: 36px;
// 辅助元素样式
.widgetBuffer {
  vertical-align: text-top;
  height: 1em;
  width: 0;
  display: inline;
}

@slotBgColor: #cfc5f2;
@slotColor: #f4f0ff;

.slot-side-left {
  background-color: @slotBgColor;
  border-radius: 6px 0 0 6px;
  margin-left: 3px;
  height: 26px;
  padding: 2px 0 2px 6px;
}

.slot-side-right {
  background-color: @slotBgColor;
  border-radius: 0 6px 6px 0;
  margin-right: 3px;
  height: 26px;
  padding: 2px 6px 2px 0;
}

.placeholder,
.slot-content {
  color: @slotColor;
  background-color: @slotBgColor;
  word-break: break-all;
  padding: 2px 0;
  line-height: 20px;
}
.slot-content {
  color: @slotColor;
}
</style>
