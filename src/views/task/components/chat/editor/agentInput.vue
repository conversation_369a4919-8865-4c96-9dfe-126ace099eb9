<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 11:28:13
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/editor/agentInput.vue
 * @Description: 可编辑的输入框组件，支持光标位置保持和placeholder显示
-->
<template>
  <div class="agentInput" :data-key="slotData.id" contenteditable="false" @keydown.delete="handleDEl">
    <span
      :class="['input-content', isEmpty(props.value) ? 'show-placeholder' : '']"
      :data-placeholder="placeholder"
      ref="inputRef"
      contenteditable="true"
      @input="handleInput"
      @blur="handleBlur"
    ></span>

    <!-- @change.stop="handleInput" -->
    <!-- @keydown.stop="handleKeydown" -->
  </div>
</template>

<script setup lang="ts">
import { useTemplateRef, watch } from 'vue'
import type { PromptItemType } from './index.vue'
import { isEmpty } from 'lodash-es'

// 组件接收的属性定义
interface Props {
  value: string | undefined // 输入框的值
  placeholder?: string // 占位符文本
  slotData: PromptItemType // 插槽数据
}

const props = defineProps<Props>()

// 定义组件触发的事件
const emit = defineEmits<{
  'update:value': [value?: string] // v-model双向绑定事件
  input: [value: string | undefined, slotData: PromptItemType] // 输入事件
  blur: [slotData: PromptItemType] // 失去焦点事件
  navigate: [direction: 'left' | 'right', slotData: PromptItemType] // 导航事件
  delete: []
}>()

// 输入框DOM引用
const inputRef = useTemplateRef('inputRef')

// 暴露给父组件的方法和属性
defineExpose({
  inputRef
})

function handleDEl(e: KeyboardEvent) {
  e.stopPropagation()
  if (isEmpty(props.value)) {
    console.log('空内容，删除输入框')
    console.log(props.value)
    emit('delete')
  }
}

watch(
  () => props.value,
  newVal => {
    console.log('newVal: ', newVal)
  }
)

// 处理输入事件
function handleInput(e: InputEvent) {
  e.stopPropagation()
  const value = inputRef.value?.innerHTML
  console.log('value: ', value)
  // 处理特殊情况：当只有一个br标签时视为空内容
  const textValue = value === '<br>' ? '' : inputRef.value?.innerText.trim() || ''
  if (isEmpty(textValue) && inputRef.value) {
    inputRef.value.innerHTML = ''
  }
  emit('update:value', textValue)
  emit('input', textValue, props.slotData)
}

// 处理失去焦点事件
function handleBlur() {
  console.log('handleBlur')
  // 失焦时检查并清理空内容
  if (inputRef.value) {
    const value = inputRef.value.innerText || ''
    if (value.trim() === '' || value === '\n') {
      inputRef.value.innerHTML = ''
      emit('update:value', '')
      emit('input', '', props.slotData)
    }
  }
  emit('blur', props.slotData)
}

// 处理键盘事件
function handleKeydown(e: KeyboardEvent) {
  console.log('e: ', e)
  // 判断删除键
  if (e.key === 'Backspace' || e.key === 'Delete') {
    // e.preventDefault()
    // return
  }

  // if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
  //   const selection = window.getSelection()
  //   if (selection && selection.rangeCount > 0) {
  //     const range = selection.getRangeAt(0)
  //     const isAtStart = range.startOffset === 0
  //     const isAtEnd = range.startContainer.textContent
  //       ? range.startOffset === range.startContainer.textContent.length
  //       : false

  //     // 在空内容时，或在内容的最左/最右边时触发导航
  //     if (
  //       (props.value && !props.value.trim()) ||
  //       (e.key === 'ArrowLeft' && isAtStart) ||
  //       (e.key === 'ArrowRight' && isAtEnd)
  //     ) {
  //       // e.preventDefault()
  //       emit('navigate', e.key === 'ArrowLeft' ? 'left' : 'right', props.slotData)
  //     }
  //   }
  // }
}
</script>

<style scoped lang="less">
// 定义插槽样式变量
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

// 输入框容器样式
.agentInput {
  display: inline-flex;
  align-items: center;
  // vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
  cursor: text;
  outline: none;

  // 输入内容区域样式
  .input-content {
    outline: none;
    min-width: 1px;
    color: #6553ee;

    // 只在空内容且有 show-placeholder 类时显示 placeholder
    &.show-placeholder:empty::before {
      content: attr(data-placeholder);
      color: #f4f0ff;
      pointer-events: none;
    }
  }
}

// 辅助元素样式
.widgetBuffer {
  vertical-align: middle;
  height: 100%;
  width: 0;
  // display: ;
}

@slotBgColor: #cfc5f2;
@slotColor: #f4f0ff;

// .slot-side-left {
//   background-color: @slotBgColor;
//   border-radius: 6px 0 0 6px;
//   margin-left: 3px;
//   height: 24px;
//   padding: 2px 0 2px 6px;
// }

// .slot-side-right {
//   background-color: @slotBgColor;
//   border-radius: 0 6px 6px 0;
//   margin-right: 3px;
//   height: 24px;
//   padding: 2px 6px 2px 0;
// }

.placeholder,
.slot-content {
  color: @slotColor;
  background-color: @slotBgColor;
  word-break: break-all;
  padding: 2px 0;
  line-height: 20px;
}
.slot-content {
  color: @slotColor;
}
</style>
