<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:34:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 18:05:41
 * @FilePath: /global-intelligence-web/src/views/task/components/group.vue
 * @Description: 
-->
<template>
  <div class="h-full flex flex-direction-column">
    <div class="p16px">
      <a-button block type="primary" @click="router.push('/task')">
        <div class="flex-center-center">
          <iconfontIcon icon="icon-add" />
          <span> 新任务 </span>
        </div>
      </a-button>
    </div>

    <p class="fs-14px color-#666 px16px mb8px">历史对话</p>
    <ul class="overflow-auto flex-1 px16px pb16px">
      <li
        :class="['groupItem clickGroupItem ellipsis', activityGroupId === '1212' ? 'bg-#f4f0ff' : '']"
        v-for="(item, index) in 100"
        :key="index"
      >
        <p>
          <iconfontIcon icon="icon-chat"></iconfontIcon>
          测试数据
        </p>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activityGroupId = ref('12')
</script>

<style lang="less" scoped>
.groupItem {
  @apply fs-16px color-#000-88 br-6px;
  + .groupItem {
    @apply mt-8px;
  }
  p {
    @apply transition-all ellipsis px-8px py-6px;
  }
}
.clickGroupItem {
  @apply cursor-pointer hover:(bg-#ebebeb);
}
</style>
